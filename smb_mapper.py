#!/usr/bin/env python3
"""
SMB Drive Mapper - Conecta a um servidor SMB com porta personalizada e mapeia como unidade no Windows
"""

import os
import sys
import subprocess
import socket
import time
from typing import Optional
import argparse
import getpass


class SMBMapper:
    def __init__(self, host: str, port: int, username: str, password: str, share_name: str = ""):
        """
        Inicializa o mapeador SMB
        
        Args:
            host: Endereço do servidor SMB
            port: Porta do servidor SMB (não padrão)
            username: Nome de usuário
            password: Senha
            share_name: Nome do compartilhamento (opcional)
        """
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.share_name = share_name
        
    def test_connection(self) -> bool:
        """
        Testa se é possível conectar ao servidor SMB na porta especificada
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((self.host, self.port))
            sock.close()
            return result == 0
        except Exception as e:
            print(f"Erro ao testar conexão: {e}")
            return False
    
    def get_available_drive_letter(self) -> Optional[str]:
        """
        Encontra uma letra de unidade disponível
        """
        used_drives = set()
        
        # Verifica unidades já em uso
        for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
            if os.path.exists(f"{letter}:\\"):
                used_drives.add(letter)
        
        # Retorna a primeira letra disponível (começando de Z para evitar conflitos)
        for letter in 'ZYXWVUTSRQPONMLKJIHGFEDCBA':
            if letter not in used_drives:
                return letter
        
        return None
    
    def map_drive(self, drive_letter: Optional[str] = None) -> bool:
        """
        Mapeia a unidade SMB usando net use do Windows
        
        Args:
            drive_letter: Letra da unidade (opcional, será escolhida automaticamente se não fornecida)
        """
        if not self.test_connection():
            print(f"Erro: Não foi possível conectar ao servidor {self.host}:{self.port}")
            return False
        
        if drive_letter is None:
            drive_letter = self.get_available_drive_letter()
            if drive_letter is None:
                print("Erro: Nenhuma letra de unidade disponível")
                return False
        
        # Constrói o caminho UNC
        if self.share_name:
            unc_path = f"\\\\{self.host}\\{self.share_name}"
        else:
            unc_path = f"\\\\{self.host}"
        
        # Comando net use para mapear a unidade
        cmd = [
            "net", "use", f"{drive_letter}:", unc_path,
            f"/user:{self.username}", self.password,
            "/persistent:no"
        ]
        
        try:
            print(f"Mapeando {unc_path} para unidade {drive_letter}:")
            
            # Para SMB com porta não padrão, precisamos usar uma abordagem diferente
            # Vamos tentar usar o comando net use com timeout
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print(f"✓ Unidade {drive_letter}: mapeada com sucesso!")
                print(f"  Caminho: {unc_path}")
                print(f"  Usuário: {self.username}")
                return True
            else:
                print(f"✗ Erro ao mapear unidade:")
                print(f"  Código de erro: {result.returncode}")
                print(f"  Saída: {result.stdout}")
                print(f"  Erro: {result.stderr}")
                
                # Tenta uma abordagem alternativa para portas não padrão
                return self._map_drive_alternative(drive_letter)
                
        except subprocess.TimeoutExpired:
            print("✗ Timeout ao tentar mapear a unidade")
            return False
        except Exception as e:
            print(f"✗ Erro inesperado: {e}")
            return False
    
    def _map_drive_alternative(self, drive_letter: str) -> bool:
        """
        Método alternativo para mapear unidades SMB com porta não padrão
        """
        print(f"\nTentando método alternativo para porta não padrão {self.port}...")
        
        # Para portas não padrão, podemos tentar usar o formato IP:porta
        unc_path_with_port = f"\\\\{self.host}:{self.port}"
        if self.share_name:
            unc_path_with_port += f"\\{self.share_name}"
        
        cmd_alt = [
            "net", "use", f"{drive_letter}:", unc_path_with_port,
            f"/user:{self.username}", self.password,
            "/persistent:no"
        ]
        
        try:
            result = subprocess.run(
                cmd_alt,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print(f"✓ Unidade {drive_letter}: mapeada com sucesso (método alternativo)!")
                return True
            else:
                print(f"✗ Método alternativo também falhou:")
                print(f"  Saída: {result.stdout}")
                print(f"  Erro: {result.stderr}")
                print(f"\n💡 Dica: Para SMB com porta não padrão, pode ser necessário:")
                print(f"   1. Configurar um túnel SSH: ssh -L 445:{self.host}:{self.port} user@gateway")
                print(f"   2. Usar software de terceiros como 'SMB/CIFS Client'")
                print(f"   3. Configurar um proxy SMB local")
                return False
                
        except Exception as e:
            print(f"✗ Erro no método alternativo: {e}")
            return False
    
    def unmap_drive(self, drive_letter: str) -> bool:
        """
        Remove o mapeamento de uma unidade
        """
        try:
            cmd = ["net", "use", f"{drive_letter}:", "/delete", "/y"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✓ Unidade {drive_letter}: desmapeada com sucesso!")
                return True
            else:
                print(f"✗ Erro ao desmapar unidade {drive_letter}:")
                print(f"  {result.stderr}")
                return False
                
        except Exception as e:
            print(f"✗ Erro ao desmapar unidade: {e}")
            return False
    
    def list_mapped_drives(self):
        """
        Lista todas as unidades mapeadas
        """
        try:
            result = subprocess.run(["net", "use"], capture_output=True, text=True)
            print("Unidades mapeadas:")
            print(result.stdout)
        except Exception as e:
            print(f"Erro ao listar unidades: {e}")


def main():
    parser = argparse.ArgumentParser(description="Mapeia unidade SMB com porta personalizada")
    parser.add_argument("host", help="Endereço do servidor SMB")
    parser.add_argument("port", type=int, help="Porta do servidor SMB")
    parser.add_argument("username", help="Nome de usuário")
    parser.add_argument("-p", "--password", help="Senha (será solicitada se não fornecida)")
    parser.add_argument("-s", "--share", help="Nome do compartilhamento", default="")
    parser.add_argument("-d", "--drive", help="Letra da unidade (ex: Z)", default=None)
    parser.add_argument("--unmap", help="Desmapar unidade especificada")
    parser.add_argument("--list", action="store_true", help="Listar unidades mapeadas")
    
    args = parser.parse_args()
    
    if args.list:
        mapper = SMBMapper("", 0, "", "")
        mapper.list_mapped_drives()
        return
    
    if args.unmap:
        mapper = SMBMapper("", 0, "", "")
        mapper.unmap_drive(args.unmap.upper())
        return
    
    # Solicita senha se não fornecida
    password = args.password
    if not password:
        password = getpass.getpass("Digite a senha: ")
    
    # Cria o mapeador
    mapper = SMBMapper(args.host, args.port, args.username, password, args.share)
    
    # Mapeia a unidade
    success = mapper.map_drive(args.drive)
    
    if success:
        print(f"\n🎉 Mapeamento concluído com sucesso!")
        print(f"Você pode acessar os arquivos através da unidade mapeada.")
    else:
        print(f"\n❌ Falha no mapeamento da unidade.")
        sys.exit(1)


if __name__ == "__main__":
    main()
