# SMB Drive Mapper

Um programa Python para conectar a servidores SMB com portas personalizadas e mapear como unidades no Windows.

## Características

- ✅ **Interface Gráfica Amigável** - Formulário visual para inserir dados
- ✅ **Interface de Linha de Comando** - Para automação e scripts
- ✅ Conecta a servidores SMB com portas não padrão
- ✅ Mapeia automaticamente uma letra de unidade disponível
- ✅ Testa conectividade antes do mapeamento
- ✅ Suporte a compartilhamentos específicos
- ✅ Desmapeamento de unidades
- ✅ Listagem de unidades mapeadas
- ✅ Log de atividades em tempo real

## Instalação

1. Clone ou baixe o arquivo `smb_mapper.py`
2. Certifique-se de ter Python 3.6+ instalado
3. Execute diretamente (usa apenas bibliotecas padrão do Python)

```bash
# Opcional: instalar dependências extras
pip install -r requirements.txt
```

## Uso

### 🖥️ Interface Gráfica (Recomendado)

```bash
# Executar com interface gráfica
python run_smb_mapper.py

# Ou diretamente
python smb_mapper_gui.py
```

A interface gráfica oferece:
- 📝 Formulário visual para inserir dados
- 🔍 Botão de teste de conexão
- 📁 Mapeamento com um clique
- 📋 Listagem de unidades mapeadas
- 🗑️ Desmapeamento fácil
- 📊 Log de atividades em tempo real

### 💻 Interface de Linha de Comando

```bash
# Exemplo básico
python smb_mapper.py ************* 2445 usuario

# Com compartilhamento específico
python smb_mapper.py ************* 2445 usuario -s "documentos"

# Especificando letra da unidade
python smb_mapper.py ************* 2445 usuario -d Z

# Com senha na linha de comando (não recomendado para produção)
python smb_mapper.py ************* 2445 usuario -p "minhasenha"
```

### Outras operações

```bash
# Listar unidades mapeadas
python smb_mapper.py --list

# Desmapar uma unidade
python smb_mapper.py --unmap Z
```

## Parâmetros

- `host`: Endereço IP ou nome do servidor SMB
- `port`: Porta do servidor SMB (ex: 2445, 8445, etc.)
- `username`: Nome de usuário para autenticação
- `-p, --password`: Senha (será solicitada se não fornecida)
- `-s, --share`: Nome do compartilhamento específico
- `-d, --drive`: Letra da unidade desejada (será escolhida automaticamente se não especificada)
- `--unmap`: Desmapar unidade especificada
- `--list`: Listar todas as unidades mapeadas

## Limitações e Soluções

### Portas Não Padrão

O Windows tem limitações para mapear unidades SMB em portas não padrão. O programa tenta várias abordagens:

1. **Método padrão**: Usa `net use` com caminho UNC
2. **Método alternativo**: Tenta incluir a porta no caminho UNC

### Soluções para Portas Não Padrão

Se o mapeamento direto falhar, considere estas alternativas:

1. **Túnel SSH**:
   ```bash
   ssh -L 445:servidor_smb:2445 usuario@gateway
   # Depois mapear para localhost:445
   ```

2. **Proxy SMB Local**: Configure um proxy local que redirecione da porta 445 para a porta customizada

3. **Software de Terceiros**: Use clientes SMB que suportam portas customizadas

## Exemplos de Uso

### Exemplo 1: Servidor NAS com porta customizada
```bash
python smb_mapper.py ************ 8445 admin -s "backup"
```

### Exemplo 2: Servidor Linux Samba
```bash
python smb_mapper.py servidor.local 2445 usuario -d Y
```

### Exemplo 3: Desmapear após uso
```bash
python smb_mapper.py ************ 8445 admin -s "backup"
# ... usar a unidade ...
python smb_mapper.py --unmap Z
```

## Troubleshooting

### Erro de Conexão
- Verifique se o servidor está acessível: `ping servidor`
- Teste a porta: `telnet servidor porta`
- Verifique firewall local e do servidor

### Erro de Autenticação
- Confirme usuário e senha
- Verifique se o usuário tem permissão no compartilhamento
- Para domínios, use: `DOMINIO\usuario`

### Erro de Mapeamento
- Execute como administrador se necessário
- Verifique se a letra da unidade não está em uso
- Para portas não padrão, considere as soluções alternativas

## Segurança

- ⚠️ Evite passar senhas na linha de comando em ambientes de produção
- 🔒 Use variáveis de ambiente ou arquivos de configuração seguros
- 🛡️ Configure conexões criptografadas quando possível

## Licença

Este código é fornecido como exemplo educacional. Use por sua conta e risco.
