#!/usr/bin/env python3
"""
SMB Drive Mapper GUI - Interface gráfica para mapear unidades SMB com porta personalizada
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import sys
import os
from smb_mapper import SMBMapper


class SMBMapperGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("SMB Drive Mapper")
        self.root.geometry("600x700")
        self.root.resizable(True, True)
        
        # Variáveis
        self.host_var = tk.StringVar()
        self.port_var = tk.StringVar(value="445")
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.share_var = tk.StringVar()
        self.drive_var = tk.StringVar()
        
        self.setup_ui()
        self.populate_drive_letters()
        
    def setup_ui(self):
        """Configura a interface do usuário"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Título
        title_label = ttk.Label(main_frame, text="SMB Drive Mapper", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Campos de entrada
        row = 1
        
        # Host
        ttk.Label(main_frame, text="Servidor (Host):").grid(row=row, column=0, sticky=tk.W, pady=5)
        host_entry = ttk.Entry(main_frame, textvariable=self.host_var, width=30)
        host_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        ttk.Label(main_frame, text="Ex: *************", foreground="gray").grid(row=row, column=2, sticky=tk.W, padx=(10, 0))
        
        row += 1
        
        # Porta
        ttk.Label(main_frame, text="Porta:").grid(row=row, column=0, sticky=tk.W, pady=5)
        port_entry = ttk.Entry(main_frame, textvariable=self.port_var, width=30)
        port_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        ttk.Label(main_frame, text="Ex: 2445, 8445", foreground="gray").grid(row=row, column=2, sticky=tk.W, padx=(10, 0))
        
        row += 1
        
        # Usuário
        ttk.Label(main_frame, text="Usuário:").grid(row=row, column=0, sticky=tk.W, pady=5)
        username_entry = ttk.Entry(main_frame, textvariable=self.username_var, width=30)
        username_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        ttk.Label(main_frame, text="Nome de usuário", foreground="gray").grid(row=row, column=2, sticky=tk.W, padx=(10, 0))
        
        row += 1
        
        # Senha
        ttk.Label(main_frame, text="Senha:").grid(row=row, column=0, sticky=tk.W, pady=5)
        password_entry = ttk.Entry(main_frame, textvariable=self.password_var, show="*", width=30)
        password_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # Botão para mostrar/ocultar senha
        self.show_password_var = tk.BooleanVar()
        show_pass_check = ttk.Checkbutton(main_frame, text="Mostrar", 
                                         variable=self.show_password_var,
                                         command=lambda: self.toggle_password(password_entry))
        show_pass_check.grid(row=row, column=2, sticky=tk.W, padx=(10, 0))
        
        row += 1
        
        # Compartilhamento (opcional)
        ttk.Label(main_frame, text="Compartilhamento:").grid(row=row, column=0, sticky=tk.W, pady=5)
        share_entry = ttk.Entry(main_frame, textvariable=self.share_var, width=30)
        share_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        ttk.Label(main_frame, text="Opcional", foreground="gray").grid(row=row, column=2, sticky=tk.W, padx=(10, 0))
        
        row += 1
        
        # Letra da unidade
        ttk.Label(main_frame, text="Letra da Unidade:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.drive_combo = ttk.Combobox(main_frame, textvariable=self.drive_var, width=27, state="readonly")
        self.drive_combo.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        ttk.Label(main_frame, text="Automático se vazio", foreground="gray").grid(row=row, column=2, sticky=tk.W, padx=(10, 0))
        
        row += 1
        
        # Separador
        ttk.Separator(main_frame, orient='horizontal').grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        
        row += 1
        
        # Botões
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=3, pady=10)
        
        # Botão de teste de conexão
        test_btn = ttk.Button(button_frame, text="🔍 Testar Conexão", command=self.test_connection)
        test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Botão de mapear
        map_btn = ttk.Button(button_frame, text="📁 Mapear Unidade", command=self.map_drive)
        map_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Botão de atualizar unidades
        refresh_btn = ttk.Button(button_frame, text="🔄 Atualizar", command=self.populate_drive_letters)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Botão de listar unidades
        list_btn = ttk.Button(button_frame, text="📋 Listar Unidades", command=self.list_drives)
        list_btn.pack(side=tk.LEFT)
        
        row += 1
        
        # Área de log
        ttk.Label(main_frame, text="Log de Atividades:", font=("Arial", 10, "bold")).grid(row=row, column=0, sticky=tk.W, pady=(20, 5))
        
        row += 1
        
        # Text widget para log
        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=70)
        self.log_text.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # Configurar expansão do log
        main_frame.rowconfigure(row, weight=1)
        
        row += 1
        
        # Frame para desmapar
        unmap_frame = ttk.LabelFrame(main_frame, text="Desmapar Unidade", padding="10")
        unmap_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        unmap_frame.columnconfigure(1, weight=1)
        
        ttk.Label(unmap_frame, text="Unidade:").grid(row=0, column=0, sticky=tk.W)
        self.unmap_var = tk.StringVar()
        unmap_combo = ttk.Combobox(unmap_frame, textvariable=self.unmap_var, width=10, state="readonly")
        unmap_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        unmap_btn = ttk.Button(unmap_frame, text="🗑️ Desmapar", command=self.unmap_drive)
        unmap_btn.grid(row=0, column=2, padx=(10, 0))
        
        # Atualizar lista de unidades mapeadas
        self.update_mapped_drives(unmap_combo)
        
        # Log inicial
        self.log("SMB Drive Mapper iniciado")
        self.log("Preencha os campos e clique em 'Mapear Unidade'")
        
    def toggle_password(self, password_entry):
        """Alterna entre mostrar/ocultar senha"""
        if self.show_password_var.get():
            password_entry.config(show="")
        else:
            password_entry.config(show="*")
    
    def populate_drive_letters(self):
        """Popula o combobox com letras de unidade disponíveis"""
        try:
            mapper = SMBMapper("", 0, "", "")
            available = []
            
            # Adicionar opção automática
            available.append("Automático")
            
            # Verificar letras disponíveis
            used_drives = set()
            for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
                if os.path.exists(f"{letter}:\\"):
                    used_drives.add(letter)
            
            # Adicionar letras disponíveis (começando de Z)
            for letter in 'ZYXWVUTSRQPONMLKJIHGFEDCBA':
                if letter not in used_drives:
                    available.append(f"{letter}:")
            
            self.drive_combo['values'] = available
            if available:
                self.drive_combo.set(available[0])
                
        except Exception as e:
            self.log(f"Erro ao buscar unidades disponíveis: {e}")
    
    def update_mapped_drives(self, combo_widget):
        """Atualiza lista de unidades mapeadas para desmapeamento"""
        try:
            mapped_drives = []
            for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
                if os.path.exists(f"{letter}:\\"):
                    mapped_drives.append(f"{letter}:")
            
            combo_widget['values'] = mapped_drives
            
        except Exception as e:
            self.log(f"Erro ao buscar unidades mapeadas: {e}")
    
    def log(self, message):
        """Adiciona mensagem ao log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def validate_inputs(self):
        """Valida os campos de entrada"""
        if not self.host_var.get().strip():
            messagebox.showerror("Erro", "Por favor, informe o servidor (host)")
            return False
        
        try:
            port = int(self.port_var.get())
            if port < 1 or port > 65535:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Erro", "Por favor, informe uma porta válida (1-65535)")
            return False
        
        if not self.username_var.get().strip():
            messagebox.showerror("Erro", "Por favor, informe o nome de usuário")
            return False
        
        if not self.password_var.get():
            messagebox.showerror("Erro", "Por favor, informe a senha")
            return False
        
        return True
    
    def test_connection(self):
        """Testa a conexão com o servidor SMB"""
        if not self.validate_inputs():
            return
        
        def test_thread():
            try:
                self.log("🔍 Testando conexão...")
                
                mapper = SMBMapper(
                    self.host_var.get().strip(),
                    int(self.port_var.get()),
                    self.username_var.get().strip(),
                    self.password_var.get(),
                    self.share_var.get().strip()
                )
                
                if mapper.test_connection():
                    self.log("✅ Conexão bem-sucedida!")
                    messagebox.showinfo("Sucesso", "Conexão estabelecida com sucesso!")
                else:
                    self.log("❌ Falha na conexão")
                    messagebox.showerror("Erro", "Não foi possível conectar ao servidor")
                    
            except Exception as e:
                self.log(f"❌ Erro no teste: {e}")
                messagebox.showerror("Erro", f"Erro durante o teste: {e}")
        
        # Executar em thread separada para não travar a interface
        threading.Thread(target=test_thread, daemon=True).start()
    
    def map_drive(self):
        """Mapeia a unidade SMB"""
        if not self.validate_inputs():
            return
        
        def map_thread():
            try:
                self.log("📁 Iniciando mapeamento...")
                
                mapper = SMBMapper(
                    self.host_var.get().strip(),
                    int(self.port_var.get()),
                    self.username_var.get().strip(),
                    self.password_var.get(),
                    self.share_var.get().strip()
                )
                
                # Determinar letra da unidade
                drive_letter = None
                if self.drive_var.get() and self.drive_var.get() != "Automático":
                    drive_letter = self.drive_var.get().replace(":", "")
                
                success = mapper.map_drive(drive_letter)
                
                if success:
                    self.log("🎉 Unidade mapeada com sucesso!")
                    messagebox.showinfo("Sucesso", "Unidade mapeada com sucesso!")
                    self.populate_drive_letters()  # Atualizar lista
                else:
                    self.log("❌ Falha no mapeamento")
                    messagebox.showerror("Erro", "Falha ao mapear a unidade")
                    
            except Exception as e:
                self.log(f"❌ Erro no mapeamento: {e}")
                messagebox.showerror("Erro", f"Erro durante o mapeamento: {e}")
        
        threading.Thread(target=map_thread, daemon=True).start()
    
    def unmap_drive(self):
        """Desmapeia uma unidade"""
        if not self.unmap_var.get():
            messagebox.showerror("Erro", "Selecione uma unidade para desmapar")
            return
        
        drive_letter = self.unmap_var.get().replace(":", "")
        
        def unmap_thread():
            try:
                self.log(f"🗑️ Desmapeando unidade {drive_letter}:")
                
                mapper = SMBMapper("", 0, "", "")
                success = mapper.unmap_drive(drive_letter)
                
                if success:
                    self.log("✅ Unidade desmapeada com sucesso!")
                    messagebox.showinfo("Sucesso", f"Unidade {drive_letter}: desmapeada com sucesso!")
                    self.populate_drive_letters()  # Atualizar lista
                else:
                    self.log("❌ Falha ao desmapar")
                    messagebox.showerror("Erro", "Falha ao desmapar a unidade")
                    
            except Exception as e:
                self.log(f"❌ Erro ao desmapar: {e}")
                messagebox.showerror("Erro", f"Erro ao desmapar: {e}")
        
        threading.Thread(target=unmap_thread, daemon=True).start()
    
    def list_drives(self):
        """Lista todas as unidades mapeadas"""
        def list_thread():
            try:
                self.log("📋 Listando unidades mapeadas...")
                mapper = SMBMapper("", 0, "", "")
                mapper.list_mapped_drives()
                
            except Exception as e:
                self.log(f"❌ Erro ao listar: {e}")
        
        threading.Thread(target=list_thread, daemon=True).start()


def main():
    """Função principal"""
    root = tk.Tk()
    app = SMBMapperGUI(root)
    
    # Configurar ícone (se disponível)
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # Centralizar janela
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()


if __name__ == "__main__":
    main()
