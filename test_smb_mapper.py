#!/usr/bin/env python3
"""
Teste do SMB Mapper - Script para testar as funcionalidades
"""

import unittest
import socket
from unittest.mock import patch, MagicMock
from smb_mapper import SMBMapper


class TestSMBMapper(unittest.TestCase):
    
    def setUp(self):
        """Configura os testes"""
        self.mapper = SMBMapper("*************", 2445, "testuser", "testpass", "testshare")
    
    def test_init(self):
        """Testa a inicialização da classe"""
        self.assertEqual(self.mapper.host, "*************")
        self.assertEqual(self.mapper.port, 2445)
        self.assertEqual(self.mapper.username, "testuser")
        self.assertEqual(self.mapper.password, "testpass")
        self.assertEqual(self.mapper.share_name, "testshare")
    
    @patch('socket.socket')
    def test_connection_success(self, mock_socket):
        """Testa conexão bem-sucedida"""
        mock_sock = MagicMock()
        mock_sock.connect_ex.return_value = 0
        mock_socket.return_value = mock_sock
        
        result = self.mapper.test_connection()
        self.assertTrue(result)
        mock_sock.connect_ex.assert_called_once_with(("*************", 2445))
    
    @patch('socket.socket')
    def test_connection_failure(self, mock_socket):
        """Testa falha na conexão"""
        mock_sock = MagicMock()
        mock_sock.connect_ex.return_value = 1  # Erro de conexão
        mock_socket.return_value = mock_sock
        
        result = self.mapper.test_connection()
        self.assertFalse(result)
    
    @patch('os.path.exists')
    def test_get_available_drive_letter(self, mock_exists):
        """Testa busca por letra de unidade disponível"""
        # Simula que apenas C: está em uso
        def side_effect(path):
            return path == "C:\\"
        
        mock_exists.side_effect = side_effect
        
        result = self.mapper.get_available_drive_letter()
        self.assertIsNotNone(result)
        self.assertNotEqual(result, "C")
    
    @patch('subprocess.run')
    @patch.object(SMBMapper, 'test_connection')
    @patch.object(SMBMapper, 'get_available_drive_letter')
    def test_map_drive_success(self, mock_get_drive, mock_test_conn, mock_subprocess):
        """Testa mapeamento bem-sucedido"""
        mock_test_conn.return_value = True
        mock_get_drive.return_value = "Z"
        
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_subprocess.return_value = mock_result
        
        result = self.mapper.map_drive()
        self.assertTrue(result)
    
    @patch.object(SMBMapper, 'test_connection')
    def test_map_drive_connection_failure(self, mock_test_conn):
        """Testa falha no mapeamento por problema de conexão"""
        mock_test_conn.return_value = False
        
        result = self.mapper.map_drive()
        self.assertFalse(result)


def test_interactive():
    """Teste interativo para verificar funcionalidades básicas"""
    print("=== Teste Interativo do SMB Mapper ===\n")
    
    # Teste de inicialização
    print("1. Testando inicialização...")
    mapper = SMBMapper("exemplo.com", 2445, "usuario", "senha", "compartilhamento")
    print(f"   ✓ Host: {mapper.host}")
    print(f"   ✓ Porta: {mapper.port}")
    print(f"   ✓ Usuário: {mapper.username}")
    print(f"   ✓ Compartilhamento: {mapper.share_name}")
    
    # Teste de busca de unidade disponível
    print("\n2. Testando busca de unidade disponível...")
    available_drive = mapper.get_available_drive_letter()
    if available_drive:
        print(f"   ✓ Unidade disponível encontrada: {available_drive}:")
    else:
        print("   ⚠️ Nenhuma unidade disponível")
    
    # Teste de listagem de unidades
    print("\n3. Testando listagem de unidades mapeadas...")
    try:
        mapper.list_mapped_drives()
        print("   ✓ Listagem executada com sucesso")
    except Exception as e:
        print(f"   ✗ Erro na listagem: {e}")
    
    print("\n=== Teste Concluído ===")
    print("\nPara testar o mapeamento real, execute:")
    print("python smb_mapper.py SEU_SERVIDOR PORTA USUARIO")


if __name__ == "__main__":
    print("Escolha o tipo de teste:")
    print("1. Testes unitários")
    print("2. Teste interativo")
    
    choice = input("Digite sua escolha (1 ou 2): ").strip()
    
    if choice == "1":
        unittest.main(argv=[''], exit=False, verbosity=2)
    elif choice == "2":
        test_interactive()
    else:
        print("Opção inválida. Executando teste interativo...")
        test_interactive()
