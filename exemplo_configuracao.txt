# Exemplo de Configurações para SMB Drive Mapper

## Configurações Comuns

### Servidor NAS Synology
Host: *************
Porta: 5445
Usuário: admin
Compartilhamento: volume1

### Servidor Windows com SMB personalizado
Host: servidor.empresa.com
Porta: 2445
Usuário: DOMINIO\usuario
Compartilhamento: documentos

### Servidor Linux Samba
Host: *********
Porta: 8445
Usuário: smbuser
Compartilhamento: shared

### Servidor FreeNAS/TrueNAS
Host: nas.local
Porta: 3445
Usuário: storage_user
Compartilhamento: backup

## Dicas de Uso

1. **Teste sempre a conexão primeiro** usando o botão "Testar Conexão"

2. **Para domínios Windows**, use o formato: DOMINIO\usuario

3. **Portas comuns não padrão**:
   - 2445 (comum em ambientes corporativos)
   - 5445 (Synology padrão alternativo)
   - 8445 (configuração personalizada)
   - 3445 (FreeNAS/TrueNAS)

4. **Compartilhamentos**:
   - Deixe vazio para acessar a raiz
   - Use nomes exatos (case-sensitive em alguns sistemas)

5. **Solução de problemas**:
   - Verifique firewall local e do servidor
   - Teste conectividade: ping + telnet host porta
   - Para portas não padrão, considere túnel SSH

## Comandos de Teste

# Testar conectividade
telnet ************* 2445

# Ping básico
ping *************

# Listar unidades mapeadas
net use
