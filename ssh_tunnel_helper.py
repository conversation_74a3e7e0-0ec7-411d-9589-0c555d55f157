#!/usr/bin/env python3
"""
SSH Tunnel Helper - Ajuda a configurar túneis SSH para SMB com portas não padrão
"""

import subprocess
import sys
import time
import socket
import threading
from typing import Optional


class SSHTunnelHelper:
    def __init__(self, ssh_host: str, ssh_user: str, smb_host: str, smb_port: int, 
                 local_port: int = 445, ssh_port: int = 22):
        """
        Inicializa o helper de túnel SSH
        
        Args:
            ssh_host: Servidor SSH (gateway)
            ssh_user: Usuário SSH
            smb_host: Servidor SMB de destino
            smb_port: Porta SMB de destino
            local_port: Porta local para o túnel (padrão: 445)
            ssh_port: Porta SSH (padrão: 22)
        """
        self.ssh_host = ssh_host
        self.ssh_user = ssh_user
        self.smb_host = smb_host
        self.smb_port = smb_port
        self.local_port = local_port
        self.ssh_port = ssh_port
        self.tunnel_process = None
        
    def check_ssh_available(self) -> bool:
        """Verifica se o SSH está disponível no sistema"""
        try:
            result = subprocess.run(["ssh", "-V"], 
                                  capture_output=True, text=True, timeout=5)
            return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def check_port_available(self, port: int) -> bool:
        """Verifica se uma porta local está disponível"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('localhost', port))
            sock.close()
            return True
        except OSError:
            return False
    
    def find_available_port(self, start_port: int = 8445) -> Optional[int]:
        """Encontra uma porta local disponível"""
        for port in range(start_port, start_port + 100):
            if self.check_port_available(port):
                return port
        return None
    
    def create_tunnel(self, background: bool = True) -> bool:
        """
        Cria o túnel SSH
        
        Args:
            background: Se True, executa em background
        """
        if not self.check_ssh_available():
            print("❌ SSH não está disponível no sistema")
            print("💡 Instale o OpenSSH Client:")
            print("   - Windows: Configurações → Apps → Recursos opcionais → OpenSSH Client")
            print("   - Ou baixe o PuTTY: https://www.putty.org/")
            return False
        
        # Verificar se a porta local está disponível
        if not self.check_port_available(self.local_port):
            print(f"⚠️ Porta local {self.local_port} não está disponível")
            alternative_port = self.find_available_port()
            if alternative_port:
                print(f"💡 Usando porta alternativa: {alternative_port}")
                self.local_port = alternative_port
            else:
                print("❌ Nenhuma porta local disponível")
                return False
        
        # Comando SSH para criar o túnel
        ssh_cmd = [
            "ssh",
            "-L", f"{self.local_port}:{self.smb_host}:{self.smb_port}",
            "-p", str(self.ssh_port),
            f"{self.ssh_user}@{self.ssh_host}",
            "-N"  # Não executar comando remoto
        ]
        
        if background:
            ssh_cmd.insert(-1, "-f")  # Executar em background
        
        try:
            print(f"🔧 Criando túnel SSH...")
            print(f"   Local: localhost:{self.local_port}")
            print(f"   Remoto: {self.smb_host}:{self.smb_port}")
            print(f"   Via: {self.ssh_user}@{self.ssh_host}:{self.ssh_port}")
            print(f"   Comando: {' '.join(ssh_cmd)}")
            
            if background:
                # Executar em background
                self.tunnel_process = subprocess.Popen(
                    ssh_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                
                # Aguardar um pouco para o túnel se estabelecer
                time.sleep(3)
                
                # Verificar se o processo ainda está rodando
                if self.tunnel_process.poll() is None:
                    print("✅ Túnel SSH criado com sucesso!")
                    print(f"💡 Agora você pode mapear: \\\\localhost:{self.local_port}")
                    return True
                else:
                    stdout, stderr = self.tunnel_process.communicate()
                    print("❌ Falha ao criar túnel SSH")
                    print(f"Erro: {stderr.decode()}")
                    return False
            else:
                # Executar em foreground (interativo)
                print("🔄 Executando túnel SSH (pressione Ctrl+C para parar)...")
                self.tunnel_process = subprocess.run(ssh_cmd)
                return True
                
        except Exception as e:
            print(f"❌ Erro ao criar túnel SSH: {e}")
            return False
    
    def stop_tunnel(self):
        """Para o túnel SSH"""
        if self.tunnel_process:
            try:
                self.tunnel_process.terminate()
                self.tunnel_process.wait(timeout=5)
                print("✅ Túnel SSH parado")
            except subprocess.TimeoutExpired:
                self.tunnel_process.kill()
                print("🔨 Túnel SSH forçadamente parado")
            except Exception as e:
                print(f"⚠️ Erro ao parar túnel: {e}")
    
    def test_tunnel(self) -> bool:
        """Testa se o túnel está funcionando"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('localhost', self.local_port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def get_mapping_command(self) -> str:
        """Retorna o comando para mapear a unidade via túnel"""
        return f"net use Z: \\\\localhost\\compartilhamento /user:usuario senha"


def main():
    """Função principal para uso interativo"""
    print("SSH Tunnel Helper para SMB")
    print("=" * 40)
    
    # Coletar informações
    ssh_host = input("Servidor SSH (gateway): ").strip()
    ssh_user = input("Usuário SSH: ").strip()
    smb_host = input("Servidor SMB de destino: ").strip()
    
    try:
        smb_port = int(input("Porta SMB de destino: ").strip())
    except ValueError:
        print("❌ Porta inválida")
        return
    
    # Criar helper
    helper = SSHTunnelHelper(ssh_host, ssh_user, smb_host, smb_port)
    
    # Verificar SSH
    if not helper.check_ssh_available():
        return
    
    # Criar túnel
    print("\nEscolha o modo:")
    print("1. Background (recomendado)")
    print("2. Foreground (interativo)")
    
    choice = input("Opção (1 ou 2): ").strip()
    background = choice != "2"
    
    success = helper.create_tunnel(background)
    
    if success and background:
        print(f"\n🎉 Túnel criado com sucesso!")
        print(f"💡 Para mapear a unidade, use:")
        print(f"   net use Z: \\\\localhost\\compartilhamento /user:usuario senha")
        print(f"\n⚠️ Para parar o túnel, execute:")
        print(f"   pkill -f 'ssh.*{helper.local_port}:{smb_host}:{smb_port}'")
        
        # Testar túnel
        if helper.test_tunnel():
            print("✅ Túnel testado e funcionando!")
        else:
            print("⚠️ Túnel criado mas teste falhou")


if __name__ == "__main__":
    main()
