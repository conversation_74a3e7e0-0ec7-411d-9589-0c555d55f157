#!/usr/bin/env python3
"""
Launcher para SMB Drive Mapper - Escolhe entre interface gráfica ou linha de comando
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Verifica se todas as dependências estão disponíveis"""
    try:
        import tkinter
        return True
    except ImportError:
        return False

def run_gui():
    """Executa a interface gráfica"""
    try:
        from smb_mapper_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"Erro ao importar interface gráfica: {e}")
        print("Certifique-se de que o arquivo smb_mapper_gui.py está presente")
        sys.exit(1)
    except Exception as e:
        print(f"Erro ao executar interface gráfica: {e}")
        sys.exit(1)

def run_cli():
    """Executa a interface de linha de comando"""
    try:
        from smb_mapper import main as cli_main
        cli_main()
    except ImportError as e:
        print(f"Erro ao importar interface de linha de comando: {e}")
        print("Certifique-se de que o arquivo smb_mapper.py está presente")
        sys.exit(1)
    except Exception as e:
        print(f"Erro ao executar interface de linha de comando: {e}")
        sys.exit(1)

def show_choice_dialog():
    """Mostra diálogo para escolher o modo de execução"""
    root = tk.Tk()
    root.withdraw()  # Oculta a janela principal
    
    choice = messagebox.askyesnocancel(
        "SMB Drive Mapper",
        "Como você deseja executar o programa?\n\n"
        "Sim = Interface Gráfica (recomendado)\n"
        "Não = Linha de Comando\n"
        "Cancelar = Sair"
    )
    
    root.destroy()
    return choice

def main():
    """Função principal do launcher"""
    print("SMB Drive Mapper - Launcher")
    print("=" * 40)
    
    # Verifica argumentos da linha de comando
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--gui', '-g']:
            print("Executando interface gráfica...")
            run_gui()
            return
        elif sys.argv[1] in ['--cli', '-c']:
            print("Executando interface de linha de comando...")
            run_cli()
            return
        elif sys.argv[1] in ['--help', '-h']:
            print("\nUso:")
            print("  python run_smb_mapper.py [opção]")
            print("\nOpções:")
            print("  --gui, -g    Executar interface gráfica")
            print("  --cli, -c    Executar interface de linha de comando")
            print("  --help, -h   Mostrar esta ajuda")
            print("\nSem argumentos: mostra diálogo de escolha")
            return
    
    # Verifica dependências
    if not check_dependencies():
        print("⚠️  Tkinter não está disponível. Executando interface de linha de comando...")
        run_cli()
        return
    
    # Mostra diálogo de escolha
    try:
        choice = show_choice_dialog()
        
        if choice is True:  # Sim - Interface Gráfica
            print("Executando interface gráfica...")
            run_gui()
        elif choice is False:  # Não - Linha de Comando
            print("Executando interface de linha de comando...")
            run_cli()
        else:  # Cancelar
            print("Execução cancelada pelo usuário.")
            
    except Exception as e:
        print(f"Erro ao mostrar diálogo: {e}")
        print("Executando interface de linha de comando como fallback...")
        run_cli()

if __name__ == "__main__":
    main()
